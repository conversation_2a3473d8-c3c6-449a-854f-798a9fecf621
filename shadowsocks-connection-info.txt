=== Shadowsocks 服務器部署完成 ===
服務器地址: ************* (srv461468751.host)
部署時間: 2025-07-09
加密方式: chacha20-ietf-poly1305

=== 4個用戶組配置 ===

Group 1 (第一組用戶):
- 端口: 8388
- 密碼: Group1Pass2025
- SS URL: ss://chacha20-ietf-poly1305:Group1Pass2025@*************:8388

Group 2 (第二組用戶):
- 端口: 8389
- 密碼: Group2Pass2025
- SS URL: ss://chacha20-ietf-poly1305:Group2Pass2025@*************:8389

Group 3 (第三組用戶):
- 端口: 8390
- 密碼: Group3Pass2025
- SS URL: ss://chacha20-ietf-poly1305:Group3Pass2025@*************:8390

Group 4 (第四組用戶):
- 端口: 8391
- 密碼: Group4Pass2025
- SS URL: ss://chacha20-ietf-poly1305:Group4Pass2025@*************:8391

=== 服務特性 ===
✅ 使用 go-shadowsocks2 (推薦版本)
✅ 自動開機啟動
✅ 服務異常自動重啟 (5秒重啟間隔)
✅ systemd 管理，穩定可靠
✅ 支援 UDP 轉發
✅ 使用 chacha20-ietf-poly1305 加密 (高性能)

=== 服務管理命令 (SSH 到服務器後執行) ===
查看狀態: systemctl status shadowsocks-group1 shadowsocks-group2 shadowsocks-group3 shadowsocks-group4
重啟服務: systemctl restart shadowsocks-group1 shadowsocks-group2 shadowsocks-group3 shadowsocks-group4
停止服務: systemctl stop shadowsocks-group1 shadowsocks-group2 shadowsocks-group3 shadowsocks-group4
啟動服務: systemctl start shadowsocks-group1 shadowsocks-group2 shadowsocks-group3 shadowsocks-group4

=== 日誌查看 ===
查看 Group 1 日誌: journalctl -u shadowsocks-group1 -f
查看 Group 2 日誌: journalctl -u shadowsocks-group2 -f
查看 Group 3 日誌: journalctl -u shadowsocks-group3 -f
查看 Group 4 日誌: journalctl -u shadowsocks-group4 -f

=== 使用說明 ===
1. 每個組可以分配給不同的用戶群組使用
2. 各組之間相互獨立，不會互相影響
3. 服務器重啟後會自動啟動所有服務
4. 如果服務異常停止，會自動重啟

=== 客戶端配置 ===
可以使用以上 SS URL 直接導入到支援 Shadowsocks 的客戶端，如：
- Clash
- V2rayN
- Shadowsocks 客戶端
- 等等
