===============================================
VPS 完整代理服務清單
===============================================
服務器信息: ************* (srv461468751.host)
更新時間: 2025-07-09 14:45 (UTC+8)
服務器位置: Singapore
操作系統: Ubuntu 22.04.5 LTS
SSH 連接: ssh root@************* (密碼: 438aMutz516_D275YC_W44)

===============================================
📊 服務總覽
===============================================
✅ Shadowsocks (SS) 服務: 4 個端口 (8388-8391)
✅ SOCKS5 (S5) 服務: 4 個端口 (1080-1083)
✅ 總計: 8 個代理服務，全部正常運行
✅ 支援用戶: 8 組獨立用戶 (每種代理 4 組)

===============================================
🛡️ SHADOWSOCKS (SS) 服務
===============================================
使用軟件: go-shadowsocks2 v0.1.5
加密方式: chacha20-ietf-poly1305
管理方式: systemd 服務

┌─ SS Group 1 ─────────────────────────────────────────────┐
│ 端口: 8388                                              │
│ 密碼: Group1Pass2025                                    │
│ 服務: shadowsocks-group1.service                       │
│ 狀態: ✅ Active (running)                              │
│ SS URL: ss://chacha20-ietf-poly1305:Group1Pass2025@*************:8388
└─────────────────────────────────────────────────────────┘

┌─ SS Group 2 ─────────────────────────────────────────────┐
│ 端口: 8389                                              │
│ 密碼: Group2Pass2025                                    │
│ 服務: shadowsocks-group2.service                       │
│ 狀態: ✅ Active (running)                              │
│ SS URL: ss://chacha20-ietf-poly1305:Group2Pass2025@*************:8389
└─────────────────────────────────────────────────────────┘

┌─ SS Group 3 ─────────────────────────────────────────────┐
│ 端口: 8390                                              │
│ 密碼: Group3Pass2025                                    │
│ 服務: shadowsocks-group3.service                       │
│ 狀態: ✅ Active (running)                              │
│ SS URL: ss://chacha20-ietf-poly1305:Group3Pass2025@*************:8390
└─────────────────────────────────────────────────────────┘

┌─ SS Group 4 ─────────────────────────────────────────────┐
│ 端口: 8391                                              │
│ 密碼: Group4Pass2025                                    │
│ 服務: shadowsocks-group4.service                       │
│ 狀態: ✅ Active (running)                              │
│ SS URL: ss://chacha20-ietf-poly1305:Group4Pass2025@*************:8391
└─────────────────────────────────────────────────────────┘

===============================================
🔌 SOCKS5 (S5) 服務
===============================================
使用軟件: gost v2.11.5
認證方式: 用戶名密碼認證
管理方式: systemd 服務

┌─ S5 Group 1 ─────────────────────────────────────────────┐
│ 端口: 1080                                              │
│ 用戶: socks5user1                                       │
│ 密碼: S5Pass2025_1                                      │
│ 服務: gost-socks5-1080.service                         │
│ 狀態: ✅ Active (running)                              │
│ 連接: socks5://socks5user1:S5Pass2025_1@*************:1080
└─────────────────────────────────────────────────────────┘

┌─ S5 Group 2 ─────────────────────────────────────────────┐
│ 端口: 1081                                              │
│ 用戶: socks5user2                                       │
│ 密碼: S5Pass2025_2                                      │
│ 服務: gost-socks5-1081.service                         │
│ 狀態: ✅ Active (running)                              │
│ 連接: socks5://socks5user2:S5Pass2025_2@*************:1081
└─────────────────────────────────────────────────────────┘

┌─ S5 Group 3 ─────────────────────────────────────────────┐
│ 端口: 1082                                              │
│ 用戶: socks5user3                                       │
│ 密碼: S5Pass2025_3                                      │
│ 服務: gost-socks5-1082.service                         │
│ 狀態: ✅ Active (running)                              │
│ 連接: socks5://socks5user3:S5Pass2025_3@*************:1082
└─────────────────────────────────────────────────────────┘

┌─ S5 Group 4 ─────────────────────────────────────────────┐
│ 端口: 1083                                              │
│ 用戶: socks5user4                                       │
│ 密碼: S5Pass2025_4                                      │
│ 服務: gost-socks5-1083.service                         │
│ 狀態: ✅ Active (running)                              │
│ 連接: socks5://socks5user4:S5Pass2025_4@*************:1083
└─────────────────────────────────────────────────────────┘

===============================================
🎯 用戶分配建議
===============================================

方案 A - 按代理類型分配:
├─ 第一組用戶: SS Group 1 (8388) + S5 Group 1 (1080)
├─ 第二組用戶: SS Group 2 (8389) + S5 Group 2 (1081)
├─ 第三組用戶: SS Group 3 (8390) + S5 Group 3 (1082)
└─ 第四組用戶: SS Group 4 (8391) + S5 Group 4 (1083)

方案 B - 純 SS 分配:
├─ 用戶組 1: SS Group 1 (8388)
├─ 用戶組 2: SS Group 2 (8389)
├─ 用戶組 3: SS Group 3 (8390)
└─ 用戶組 4: SS Group 4 (8391)

方案 C - 純 S5 分配:
├─ 用戶組 1: S5 Group 1 (1080)
├─ 用戶組 2: S5 Group 2 (1081)
├─ 用戶組 3: S5 Group 3 (1082)
└─ 用戶組 4: S5 Group 4 (1083)

===============================================
📱 客戶端配置示例
===============================================

=== Clash 配置 ===
proxies:
  # Shadowsocks 配置
  - name: "SS-Group1"
    type: ss
    server: *************
    port: 8388
    cipher: chacha20-ietf-poly1305
    password: Group1Pass2025
    
  - name: "SS-Group2"
    type: ss
    server: *************
    port: 8389
    cipher: chacha20-ietf-poly1305
    password: Group2Pass2025
    
  # SOCKS5 配置
  - name: "S5-Group1"
    type: socks5
    server: *************
    port: 1080
    username: socks5user1
    password: S5Pass2025_1
    
  - name: "S5-Group2"
    type: socks5
    server: *************
    port: 1081
    username: socks5user2
    password: S5Pass2025_2

=== V2rayN 配置 ===
Shadowsocks:
- 地址: *************
- 端口: 8388 (或其他 SS 端口)
- 密碼: Group1Pass2025 (對應組別)
- 加密: chacha20-ietf-poly1305

SOCKS5:
- 地址: *************
- 端口: 1080 (或其他 S5 端口)
- 用戶名: socks5user1 (對應組別)
- 密碼: S5Pass2025_1 (對應組別)

===============================================
🔧 服務管理命令
===============================================

=== 查看服務狀態 ===
# 查看所有 SS 服務
systemctl status shadowsocks-group1 shadowsocks-group2 shadowsocks-group3 shadowsocks-group4

# 查看所有 S5 服務
systemctl status gost-socks5-1080 gost-socks5-1081 gost-socks5-1082 gost-socks5-1083

# 查看端口監聽狀態
netstat -tlnp | grep -E '(838[8-9]|839[0-1]|108[0-3])'

=== 重啟服務 ===
# 重啟所有 SS 服務
systemctl restart shadowsocks-group1 shadowsocks-group2 shadowsocks-group3 shadowsocks-group4

# 重啟所有 S5 服務
systemctl restart gost-socks5-1080 gost-socks5-1081 gost-socks5-1082 gost-socks5-1083

=== 查看日誌 ===
# SS 服務日誌
journalctl -u shadowsocks-group1 -f

# S5 服務日誌
journalctl -u gost-socks5-1080 -f

===============================================
🚀 服務特性
===============================================

Shadowsocks 特性:
✅ 使用 go-shadowsocks2 (您偏好的版本)
✅ chacha20-ietf-poly1305 加密 (高性能)
✅ 支援 UDP 轉發
✅ 自動開機啟動
✅ 異常自動重啟 (5秒間隔)

SOCKS5 特性:
✅ 使用 gost (高性能 Go 語言工具)
✅ 用戶名密碼認證
✅ 支援 TCP 和 UDP 轉發
✅ 多用戶隔離
✅ 自動開機啟動
✅ 異常自動重啟 (5秒間隔)

===============================================
📈 監控與維護
===============================================

定期檢查項目:
□ 服務運行狀態
□ 端口監聽狀態
□ 系統資源使用
□ 日誌錯誤信息
□ 網絡連通性測試

維護建議:
□ 定期備份配置文件
□ 監控服務器性能
□ 更新軟件版本
□ 檢查安全設置
□ 清理日誌文件

===============================================
