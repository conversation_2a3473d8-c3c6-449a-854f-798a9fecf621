===============================================
VPS 代理服務完整清單
===============================================
服務器信息: ************* (srv461468751.host)
檢查時間: 2025-07-09 14:18 (UTC+8)
服務器位置: Singapore
操作系統: Ubuntu 22.04.5 LTS

===============================================
🔍 代理服務檢查結果
===============================================

📊 服務狀態總覽:
✅ Shadowsocks 服務: 4 個 (全部正常運行)
✅ SOCKS5 服務: 4 個 (全部正常運行)
❌ 其他代理服務: 0 個 (未部署)

===============================================
🛡️ Shadowsocks (SS) 服務詳情
===============================================

使用軟件: go-shadowsocks2 (推薦版本)
加密方式: chacha20-ietf-poly1305
管理方式: systemd 服務
自動啟動: ✅ 已啟用
自動重啟: ✅ 已配置 (5秒間隔)

📋 4個用戶組配置:

┌─────────────────────────────────────────────────────────┐
│ Group 1 (第一組用戶)                                    │
├─────────────────────────────────────────────────────────┤
│ 服務名稱: shadowsocks-group1.service                   │
│ 監聽端口: 8388                                         │
│ 密碼: Group1Pass2025                                   │
│ 狀態: ✅ Active (running)                              │
│ 進程ID: 780                                            │
│ SS URL: ss://chacha20-ietf-poly1305:Group1Pass2025@*************:8388
└─────────────────────────────────────────────────────────┘

┌─────────────────────────────────────────────────────────┐
│ Group 2 (第二組用戶)                                    │
├─────────────────────────────────────────────────────────┤
│ 服務名稱: shadowsocks-group2.service                   │
│ 監聽端口: 8389                                         │
│ 密碼: Group2Pass2025                                   │
│ 狀態: ✅ Active (running)                              │
│ 進程ID: 777                                            │
│ SS URL: ss://chacha20-ietf-poly1305:Group2Pass2025@*************:8389
└─────────────────────────────────────────────────────────┘

┌─────────────────────────────────────────────────────────┐
│ Group 3 (第三組用戶)                                    │
├─────────────────────────────────────────────────────────┤
│ 服務名稱: shadowsocks-group3.service                   │
│ 監聽端口: 8390                                         │
│ 密碼: Group3Pass2025                                   │
│ 狀態: ✅ Active (running)                              │
│ 進程ID: 778                                            │
│ SS URL: ss://chacha20-ietf-poly1305:Group3Pass2025@*************:8390
└─────────────────────────────────────────────────────────┘

┌─────────────────────────────────────────────────────────┐
│ Group 4 (第四組用戶)                                    │
├─────────────────────────────────────────────────────────┤
│ 服務名稱: shadowsocks-group4.service                   │
│ 監聽端口: 8391                                         │
│ 密碼: Group4Pass2025                                   │
│ 狀態: ✅ Active (running)                              │
│ 進程ID: 796                                            │
│ SS URL: ss://chacha20-ietf-poly1305:Group4Pass2025@*************:8391
└─────────────────────────────────────────────────────────┘

===============================================
🔌 SOCKS5 服務詳情
===============================================

使用軟件: gost 2.11.5 (高性能 Go 語言代理工具)
認證方式: 用戶名密碼認證
管理方式: systemd 服務
自動啟動: ✅ 已啟用
自動重啟: ✅ 已配置 (5秒間隔)

📋 4個用戶組配置:

┌─────────────────────────────────────────────────────────┐
│ Group 1 (第一組用戶)                                    │
├─────────────────────────────────────────────────────────┤
│ 服務名稱: gost-socks5-1080.service                     │
│ 監聽端口: 1080                                         │
│ 用戶名: socks5user1                                    │
│ 密碼: S5Pass2025_1                                     │
│ 狀態: ✅ Active (running)                              │
│ 進程ID: 3603                                           │
│ 連接格式: socks5://socks5user1:S5Pass2025_1@*************:1080
└─────────────────────────────────────────────────────────┘

┌─────────────────────────────────────────────────────────┐
│ Group 2 (第二組用戶)                                    │
├─────────────────────────────────────────────────────────┤
│ 服務名稱: gost-socks5-1081.service                     │
│ 監聽端口: 1081                                         │
│ 用戶名: socks5user2                                    │
│ 密碼: S5Pass2025_2                                     │
│ 狀態: ✅ Active (running)                              │
│ 進程ID: 3608                                           │
│ 連接格式: socks5://socks5user2:S5Pass2025_2@*************:1081
└─────────────────────────────────────────────────────────┘

┌─────────────────────────────────────────────────────────┐
│ Group 3 (第三組用戶)                                    │
├─────────────────────────────────────────────────────────┤
│ 服務名稱: gost-socks5-1082.service                     │
│ 監聽端口: 1082                                         │
│ 用戶名: socks5user3                                    │
│ 密碼: S5Pass2025_3                                     │
│ 狀態: ✅ Active (running)                              │
│ 進程ID: 3614                                           │
│ 連接格式: socks5://socks5user3:S5Pass2025_3@*************:1082
└─────────────────────────────────────────────────────────┘

┌─────────────────────────────────────────────────────────┐
│ Group 4 (第四組用戶)                                    │
├─────────────────────────────────────────────────────────┤
│ 服務名稱: gost-socks5-1083.service                     │
│ 監聽端口: 1083                                         │
│ 用戶名: socks5user4                                    │
│ 密碼: S5Pass2025_4                                     │
│ 狀態: ✅ Active (running)                              │
│ 進程ID: 3619                                           │
│ 連接格式: socks5://socks5user4:S5Pass2025_4@*************:1083
└─────────────────────────────────────────────────────────┘

===============================================
🌐 服務器網絡端口使用情況
===============================================

系統服務端口:
- 22/tcp    : SSH 服務 (sshd)
- 53/tcp    : DNS 解析 (systemd-resolve, 僅本地)

代理服務端口:
- 1080/tcp  : SOCKS5 Group 1 ✅
- 1081/tcp  : SOCKS5 Group 2 ✅
- 1082/tcp  : SOCKS5 Group 3 ✅
- 1083/tcp  : SOCKS5 Group 4 ✅
- 8388/tcp  : Shadowsocks Group 1 ✅
- 8389/tcp  : Shadowsocks Group 2 ✅
- 8390/tcp  : Shadowsocks Group 3 ✅
- 8391/tcp  : Shadowsocks Group 4 ✅

可用端口範圍:
- 1084-1090 : 可用於額外 SOCKS5 服務
- 8080-8087 : 可用於其他代理服務
- 8392-8399 : 可用於額外 Shadowsocks 服務
- 9000+     : 大量可用端口

===============================================
📝 服務管理命令
===============================================

Shadowsocks 服務管理:
查看狀態: systemctl status shadowsocks-group1 shadowsocks-group2 shadowsocks-group3 shadowsocks-group4
重啟服務: systemctl restart shadowsocks-group1 shadowsocks-group2 shadowsocks-group3 shadowsocks-group4
停止服務: systemctl stop shadowsocks-group1 shadowsocks-group2 shadowsocks-group3 shadowsocks-group4
啟動服務: systemctl start shadowsocks-group1 shadowsocks-group2 shadowsocks-group3 shadowsocks-group4

SOCKS5 服務管理:
查看狀態: systemctl status gost-socks5-1080 gost-socks5-1081 gost-socks5-1082 gost-socks5-1083
重啟服務: systemctl restart gost-socks5-1080 gost-socks5-1081 gost-socks5-1082 gost-socks5-1083
停止服務: systemctl stop gost-socks5-1080 gost-socks5-1081 gost-socks5-1082 gost-socks5-1083
啟動服務: systemctl start gost-socks5-1080 gost-socks5-1081 gost-socks5-1082 gost-socks5-1083

查看服務日誌:
Shadowsocks: journalctl -u shadowsocks-group1 -f (替換 group1 為其他組)
SOCKS5: journalctl -u gost-socks5-1080 -f (替換 1080 為其他端口)

===============================================
🎯 總結與建議
===============================================

✅ 當前狀態:
- 4 個 Shadowsocks 服務全部正常運行
- 4 個 SOCKS5 服務全部正常運行
- 服務配置穩定，支持自動重啟
- 端口分配合理，無衝突
- 多用戶認證配置完成

💡 可擴展選項:
- 可新增更多 SOCKS5 端口 (1084-1090)
- 可新增更多 SS 端口 (8392-8399)
- 可配置負載均衡或故障轉移
- 可添加流量統計和監控

🔧 維護建議:
- 定期檢查服務狀態
- 監控端口使用情況
- 備份服務配置文件
- 定期更新 go-shadowsocks2 和 gost
- 監控用戶連接和流量使用

===============================================
