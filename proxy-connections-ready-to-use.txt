===============================================
代理服務連接信息 - 即用版
===============================================
服務器: ************* (srv461468751.host)
更新: 2025-07-09

===============================================
🛡️ SHADOWSOCKS (SS) 連接信息
===============================================

Group 1:
ss://chacha20-ietf-poly1305:Group1Pass2025@*************:8388

Group 2:
ss://chacha20-ietf-poly1305:Group2Pass2025@*************:8389

Group 3:
ss://chacha20-ietf-poly1305:Group3Pass2025@*************:8390

Group 4:
ss://chacha20-ietf-poly1305:Group4Pass2025@*************:8391

===============================================
🔌 SOCKS5 (S5) 連接信息
===============================================

Group 1:
socks5://socks5user1:S5Pass2025_1@*************:1080

Group 2:
socks5://socks5user2:S5Pass2025_2@*************:1081

Group 3:
socks5://socks5user3:S5Pass2025_3@*************:1082

Group 4:
socks5://socks5user4:S5Pass2025_4@*************:1083

===============================================
📋 分組配置表
===============================================

┌─────────┬─────────────┬─────────────────────────────────────────┐
│  組別   │   SS 端口   │              SS 密碼                    │
├─────────┼─────────────┼─────────────────────────────────────────┤
│ Group 1 │    8388     │           Group1Pass2025               │
│ Group 2 │    8389     │           Group2Pass2025               │
│ Group 3 │    8390     │           Group3Pass2025               │
│ Group 4 │    8391     │           Group4Pass2025               │
└─────────┴─────────────┴─────────────────────────────────────────┘

┌─────────┬─────────────┬─────────────────┬─────────────────────────┐
│  組別   │   S5 端口   │    S5 用戶名    │        S5 密碼          │
├─────────┼─────────────┼─────────────────┼─────────────────────────┤
│ Group 1 │    1080     │   socks5user1   │      S5Pass2025_1       │
│ Group 2 │    1081     │   socks5user2   │      S5Pass2025_2       │
│ Group 3 │    1082     │   socks5user3   │      S5Pass2025_3       │
│ Group 4 │    1083     │   socks5user4   │      S5Pass2025_4       │
└─────────┴─────────────┴─────────────────┴─────────────────────────┘

===============================================
🎯 推薦分配方案
===============================================

方案 A - 每組用戶同時獲得 SS + S5:
├─ 第一組: SS(8388) + S5(1080)
├─ 第二組: SS(8389) + S5(1081)
├─ 第三組: SS(8390) + S5(1082)
└─ 第四組: SS(8391) + S5(1083)

方案 B - 8組用戶各用一個端口:
├─ 用戶1: SS(8388)    ├─ 用戶5: S5(1080)
├─ 用戶2: SS(8389)    ├─ 用戶6: S5(1081)
├─ 用戶3: SS(8390)    ├─ 用戶7: S5(1082)
└─ 用戶4: SS(8391)    └─ 用戶8: S5(1083)

===============================================
📱 Clash 配置 (複製即用)
===============================================

proxies:
  # Shadowsocks 服務
  - name: "SS-Group1"
    type: ss
    server: *************
    port: 8388
    cipher: chacha20-ietf-poly1305
    password: Group1Pass2025
    
  - name: "SS-Group2"
    type: ss
    server: *************
    port: 8389
    cipher: chacha20-ietf-poly1305
    password: Group2Pass2025
    
  - name: "SS-Group3"
    type: ss
    server: *************
    port: 8390
    cipher: chacha20-ietf-poly1305
    password: Group3Pass2025
    
  - name: "SS-Group4"
    type: ss
    server: *************
    port: 8391
    cipher: chacha20-ietf-poly1305
    password: Group4Pass2025
    
  # SOCKS5 服務
  - name: "S5-Group1"
    type: socks5
    server: *************
    port: 1080
    username: socks5user1
    password: S5Pass2025_1
    
  - name: "S5-Group2"
    type: socks5
    server: *************
    port: 1081
    username: socks5user2
    password: S5Pass2025_2
    
  - name: "S5-Group3"
    type: socks5
    server: *************
    port: 1082
    username: socks5user3
    password: S5Pass2025_3
    
  - name: "S5-Group4"
    type: socks5
    server: *************
    port: 1083
    username: socks5user4
    password: S5Pass2025_4

proxy-groups:
  - name: "代理選擇"
    type: select
    proxies:
      - SS-Group1
      - SS-Group2
      - SS-Group3
      - SS-Group4
      - S5-Group1
      - S5-Group2
      - S5-Group3
      - S5-Group4

===============================================
🔧 快速測試命令
===============================================

測試 SS 連接:
curl --proxy socks5://127.0.0.1:1080 http://httpbin.org/ip
(需要先在本地啟動 SS 客戶端)

測試 S5 連接:
curl --socks5 socks5user1:S5Pass2025_1@*************:1080 http://httpbin.org/ip

===============================================
📞 技術支援信息
===============================================

服務器 SSH: ssh root@*************
服務器密碼: 438aMutz516_D275YC_W44

檢查服務狀態:
systemctl status shadowsocks-group1 gost-socks5-1080

查看服務日誌:
journalctl -u shadowsocks-group1 -f
journalctl -u gost-socks5-1080 -f

重啟服務:
systemctl restart shadowsocks-group1
systemctl restart gost-socks5-1080

===============================================
