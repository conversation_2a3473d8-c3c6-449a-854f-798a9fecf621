=== SOCKS5 服務部署完成 ===
服務器地址: ************* (srv461468751.host)
部署時間: 2025-07-09
使用軟件: gost 2.11.5 (高性能代理工具)

=== 4個 SOCKS5 用戶組配置 ===

Group 1 (第一組用戶):
- 端口: 1080
- 用戶名: socks5user1
- 密碼: S5Pass2025_1
- 連接格式: socks5://socks5user1:S5Pass2025_1@*************:1080

Group 2 (第二組用戶):
- 端口: 1081
- 用戶名: socks5user2
- 密碼: S5Pass2025_2
- 連接格式: socks5://socks5user2:S5Pass2025_2@*************:1081

Group 3 (第三組用戶):
- 端口: 1082
- 用戶名: socks5user3
- 密碼: S5Pass2025_3
- 連接格式: socks5://socks5user3:S5Pass2025_3@*************:1082

Group 4 (第四組用戶):
- 端口: 1083
- 用戶名: socks5user4
- 密碼: S5Pass2025_4
- 連接格式: socks5://socks5user4:S5Pass2025_4@*************:1083

=== 服務特性 ===
✅ 使用 gost 2.11.5 (高性能 Go 語言代理工具)
✅ 支援用戶名密碼認證
✅ 自動開機啟動
✅ 服務異常自動重啟 (5秒重啟間隔)
✅ systemd 管理，穩定可靠
✅ 支援 TCP 和 UDP 轉發
✅ 多用戶隔離，各組獨立運行

=== 客戶端配置示例 ===

Clash 配置:
proxies:
  - name: "SOCKS5-Group1"
    type: socks5
    server: *************
    port: 1080
    username: socks5user1
    password: S5Pass2025_1
    
  - name: "SOCKS5-Group2"
    type: socks5
    server: *************
    port: 1081
    username: socks5user2
    password: S5Pass2025_2

V2rayN 配置:
- 協議: SOCKS
- 地址: *************
- 端口: 1080 (或 1081/1082/1083)
- 用戶名: socks5user1 (對應組別)
- 密碼: S5Pass2025_1 (對應組別)

=== 服務管理命令 (SSH 到服務器後執行) ===
查看狀態: systemctl status gost-socks5-1080 gost-socks5-1081 gost-socks5-1082 gost-socks5-1083
重啟服務: systemctl restart gost-socks5-1080 gost-socks5-1081 gost-socks5-1082 gost-socks5-1083
停止服務: systemctl stop gost-socks5-1080 gost-socks5-1081 gost-socks5-1082 gost-socks5-1083
啟動服務: systemctl start gost-socks5-1080 gost-socks5-1081 gost-socks5-1082 gost-socks5-1083

=== 日誌查看 ===
查看 Group 1 日誌: journalctl -u gost-socks5-1080 -f
查看 Group 2 日誌: journalctl -u gost-socks5-1081 -f
查看 Group 3 日誌: journalctl -u gost-socks5-1082 -f
查看 Group 4 日誌: journalctl -u gost-socks5-1083 -f

=== 使用說明 ===
1. 每個組可以分配給不同的用戶群組使用
2. 各組之間相互獨立，不會互相影響
3. 服務器重啟後會自動啟動所有服務
4. 如果服務異常停止，會自動重啟
5. 支援 TCP 和 UDP 協議轉發
6. 使用用戶名密碼認證，安全性較高

=== 測試連接 ===
可以使用以下工具測試連接:
- curl --socks5 socks5user1:S5Pass2025_1@*************:1080 http://httpbin.org/ip
- 或使用支援 SOCKS5 的瀏覽器插件進行測試
