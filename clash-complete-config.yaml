# Clash 完整配置 - 整合 SS + SOCKS5 服務
# 服務器: ************* (srv461468751.host) - Singapore
# 生成時間: 2025-07-09
# 包含: 4個 Shadowsocks + 4個 SOCKS5 = 8個代理節點

port: 7890
socks-port: 7891
allow-lan: true
mode: Rule
log-level: info
external-controller: 127.0.0.1:9090
interface-name: en0

# DNS 配置
dns:
  enable: true
  ipv6: false
  listen: 0.0.0.0:53
  enhanced-mode: fake-ip
  fake-ip-range: **********/16
  nameserver:
    - *******
    - *******
    - **************
  fallback:
    - tls://*******
    - https://*******/dns-query
    - tls://dns.google

proxies:
  # ========== Shadowsocks 服務 (4個端口) ==========
  - name: "🛡️ SS-Group1"
    type: ss
    server: *************
    port: 8388
    cipher: chacha20-ietf-poly1305
    password: Group1Pass2025
    udp: true

  - name: "🛡️ SS-Group2"
    type: ss
    server: *************
    port: 8389
    cipher: chacha20-ietf-poly1305
    password: Group2Pass2025
    udp: true

  - name: "🛡️ SS-Group3"
    type: ss
    server: *************
    port: 8390
    cipher: chacha20-ietf-poly1305
    password: Group3Pass2025
    udp: true

  - name: "🛡️ SS-Group4"
    type: ss
    server: *************
    port: 8391
    cipher: chacha20-ietf-poly1305
    password: Group4Pass2025
    udp: true

  # ========== SOCKS5 服務 (4個端口) ==========
  - name: "🔌 S5-Group1"
    type: socks5
    server: *************
    port: 1080
    username: socks5user1
    password: S5Pass2025_1
    udp: true

  - name: "🔌 S5-Group2"
    type: socks5
    server: *************
    port: 1081
    username: socks5user2
    password: S5Pass2025_2
    udp: true

  - name: "🔌 S5-Group3"
    type: socks5
    server: *************
    port: 1082
    username: socks5user3
    password: S5Pass2025_3
    udp: true

  - name: "🔌 S5-Group4"
    type: socks5
    server: *************
    port: 1083
    username: socks5user4
    password: S5Pass2025_4
    udp: true

proxy-groups:
  # ========== 主要代理選擇組 ==========
  - name: "🚀 代理選擇"
    type: select
    proxies:
      - 🎯 自動選擇
      - 🛡️ Shadowsocks組
      - 🔌 SOCKS5組
      - ⚖️ 負載均衡
      - 🔄 故障轉移
      - 🛡️ SS-Group1
      - 🛡️ SS-Group2
      - 🛡️ SS-Group3
      - 🛡️ SS-Group4
      - 🔌 S5-Group1
      - 🔌 S5-Group2
      - 🔌 S5-Group3
      - 🔌 S5-Group4
      - DIRECT

  # ========== 自動選擇組 (所有節點) ==========
  - name: "🎯 自動選擇"
    type: url-test
    proxies:
      - 🛡️ SS-Group1
      - 🛡️ SS-Group2
      - 🛡️ SS-Group3
      - 🛡️ SS-Group4
      - 🔌 S5-Group1
      - 🔌 S5-Group2
      - 🔌 S5-Group3
      - 🔌 S5-Group4
    url: 'http://www.gstatic.com/generate_204'
    interval: 300
    tolerance: 50

  # ========== Shadowsocks 專用組 ==========
  - name: "🛡️ Shadowsocks組"
    type: select
    proxies:
      - 🛡️ SS-Auto
      - 🛡️ SS-Group1
      - 🛡️ SS-Group2
      - 🛡️ SS-Group3
      - 🛡️ SS-Group4

  - name: "🛡️ SS-Auto"
    type: url-test
    proxies:
      - 🛡️ SS-Group1
      - 🛡️ SS-Group2
      - 🛡️ SS-Group3
      - 🛡️ SS-Group4
    url: 'http://www.gstatic.com/generate_204'
    interval: 300
    tolerance: 50

  # ========== SOCKS5 專用組 ==========
  - name: "🔌 SOCKS5組"
    type: select
    proxies:
      - 🔌 S5-Auto
      - 🔌 S5-Group1
      - 🔌 S5-Group2
      - 🔌 S5-Group3
      - 🔌 S5-Group4

  - name: "🔌 S5-Auto"
    type: url-test
    proxies:
      - 🔌 S5-Group1
      - 🔌 S5-Group2
      - 🔌 S5-Group3
      - 🔌 S5-Group4
    url: 'http://www.gstatic.com/generate_204'
    interval: 300
    tolerance: 50

  # ========== 負載均衡組 ==========
  - name: "⚖️ 負載均衡"
    type: load-balance
    proxies:
      - 🛡️ SS-Group1
      - 🛡️ SS-Group2
      - 🛡️ SS-Group3
      - 🛡️ SS-Group4
      - 🔌 S5-Group1
      - 🔌 S5-Group2
      - 🔌 S5-Group3
      - 🔌 S5-Group4
    url: 'http://www.gstatic.com/generate_204'
    interval: 300
    strategy: consistent-hashing

  # ========== 故障轉移組 ==========
  - name: "🔄 故障轉移"
    type: fallback
    proxies:
      - 🛡️ SS-Group1
      - 🛡️ SS-Group2
      - 🔌 S5-Group1
      - 🔌 S5-Group2
      - 🛡️ SS-Group3
      - 🛡️ SS-Group4
      - 🔌 S5-Group3
      - 🔌 S5-Group4
    url: 'http://www.gstatic.com/generate_204'
    interval: 300

rules:
  # ========== 本地網絡直連 ==========
  - DOMAIN-SUFFIX,local,DIRECT
  - IP-CIDR,*********/8,DIRECT
  - IP-CIDR,**********/12,DIRECT
  - IP-CIDR,***********/16,DIRECT
  - IP-CIDR,10.0.0.0/8,DIRECT
  - IP-CIDR,********/8,DIRECT
  - IP-CIDR,**********/10,DIRECT

  # ========== 常用國外服務 ==========
  - DOMAIN-SUFFIX,google.com,🚀 代理選擇
  - DOMAIN-SUFFIX,googleapis.com,🚀 代理選擇
  - DOMAIN-SUFFIX,googleusercontent.com,🚀 代理選擇
  - DOMAIN-SUFFIX,youtube.com,🚀 代理選擇
  - DOMAIN-SUFFIX,ytimg.com,🚀 代理選擇
  - DOMAIN-SUFFIX,facebook.com,🚀 代理選擇
  - DOMAIN-SUFFIX,fbcdn.net,🚀 代理選擇
  - DOMAIN-SUFFIX,instagram.com,🚀 代理選擇
  - DOMAIN-SUFFIX,twitter.com,🚀 代理選擇
  - DOMAIN-SUFFIX,x.com,🚀 代理選擇
  - DOMAIN-SUFFIX,telegram.org,🚀 代理選擇
  - DOMAIN-SUFFIX,t.me,🚀 代理選擇
  - DOMAIN-SUFFIX,whatsapp.com,🚀 代理選擇
  - DOMAIN-SUFFIX,discord.com,🚀 代理選擇
  - DOMAIN-SUFFIX,reddit.com,🚀 代理選擇
  - DOMAIN-SUFFIX,netflix.com,🚀 代理選擇
  - DOMAIN-SUFFIX,spotify.com,🚀 代理選擇
  - DOMAIN-SUFFIX,github.com,🚀 代理選擇
  - DOMAIN-SUFFIX,stackoverflow.com,🚀 代理選擇
  - DOMAIN-SUFFIX,medium.com,🚀 代理選擇
  - DOMAIN-SUFFIX,dropbox.com,🚀 代理選擇
  - DOMAIN-SUFFIX,onedrive.live.com,🚀 代理選擇
  - DOMAIN-SUFFIX,googledrive.com,🚀 代理選擇
  - DOMAIN-SUFFIX,icloud.com,🚀 代理選擇

  # ========== 加密貨幣相關 ==========
  - DOMAIN-SUFFIX,coinbase.com,🚀 代理選擇
  - DOMAIN-SUFFIX,binance.com,🚀 代理選擇
  - DOMAIN-SUFFIX,kraken.com,🚀 代理選擇
  - DOMAIN-SUFFIX,bitfinex.com,🚀 代理選擇
  - DOMAIN-SUFFIX,bitmex.com,🚀 代理選擇
  - DOMAIN-SUFFIX,okx.com,🚀 代理選擇
  - DOMAIN-SUFFIX,huobi.com,🚀 代理選擇
  - DOMAIN-SUFFIX,gate.io,🚀 代理選擇
  - DOMAIN-SUFFIX,kucoin.com,🚀 代理選擇
  - DOMAIN-SUFFIX,bybit.com,🚀 代理選擇
  - DOMAIN-SUFFIX,uniswap.org,🚀 代理選擇
  - DOMAIN-SUFFIX,opensea.io,🚀 代理選擇
  - DOMAIN-SUFFIX,metamask.io,🚀 代理選擇
  - DOMAIN-SUFFIX,etherscan.io,🚀 代理選擇
  - DOMAIN-SUFFIX,coingecko.com,🚀 代理選擇
  - DOMAIN-SUFFIX,coinmarketcap.com,🚀 代理選擇

  # ========== 區塊鏈相關 ==========
  - DOMAIN-SUFFIX,chainlink.com,🚀 代理選擇
  - DOMAIN-SUFFIX,polygon.technology,🚀 代理選擇
  - DOMAIN-SUFFIX,avalanche.org,🚀 代理選擇
  - DOMAIN-SUFFIX,solana.com,🚀 代理選擇
  - DOMAIN-SUFFIX,cardano.org,🚀 代理選擇
  - DOMAIN-SUFFIX,polkadot.network,🚀 代理選擇
  - DOMAIN-SUFFIX,cosmos.network,🚀 代理選擇
  - DOMAIN-SUFFIX,near.org,🚀 代理選擇
  - DOMAIN-SUFFIX,algorand.com,🚀 代理選擇
  - DOMAIN-SUFFIX,tezos.com,🚀 代理選擇
  - DOMAIN-SUFFIX,stellar.org,🚀 代理選擇
  - DOMAIN-SUFFIX,ripple.com,🚀 代理選擇

  # ========== 中國大陸網站直連 ==========
  - GEOIP,CN,DIRECT
  
  # ========== 其他流量走代理 ==========
  - MATCH,🚀 代理選擇

# ========== 配置說明 ==========
#
# 🚀 代理選擇: 主要選擇組，包含所有代理選項
# 🎯 自動選擇: 自動測速選擇最快節點
# 🛡️ Shadowsocks組: 只使用 SS 節點
# 🔌 SOCKS5組: 只使用 SOCKS5 節點
# ⚖️ 負載均衡: 流量分散到多個節點
# 🔄 故障轉移: 節點故障時自動切換
#
# 節點說明:
# SS-Group1~4: Shadowsocks 節點 (端口 8388-8391)
# S5-Group1~4: SOCKS5 節點 (端口 1080-1083)
#
# 使用建議:
# 1. 日常使用選擇 "🎯 自動選擇"
# 2. 需要穩定性選擇 "🔄 故障轉移"
# 3. 需要速度選擇 "⚖️ 負載均衡"
# 4. 特定需求選擇對應的專用組
